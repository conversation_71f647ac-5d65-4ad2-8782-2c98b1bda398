import { Exercise } from "../types/api/exercises.types";

export type PeriodizationEntry = {
  week: number;
  assignmentDate: string;
  exerciseType: "MT exercise" | "MW exercise" | "visualization";
  exerciseName: string;
  exerciseId: string | number;
};

/**
 * Periodization algorithm:
 * Exercise ID structure:
 * [type: mt (mental toughness)/mw (mental wellness)]-[skill: (b1/b2/...)]-[difficulty: (0/1/2/3) exercise identifier: (a/b/c)]
 * So therefore, Exercise ID mt-b1-0a is a mental toughness exercise for skill b1, difficulty 0, exercise a
 *
 * Run validation on start and end dates:
 * - Minimum of 4 weeks for off season
 * - Minimum of 4 weeks for prep
 * - Minimum of 2 weeks for pre-comp
 *
 * Schedule mental toughness:
 * First, group all exercises into skill buckets sorted by difficulty and then alphabetically.
 * Mental toughness will have the following skills: B1, B2, B3, C1, C2, C3, C4, C5
 * For example:
 * B1_bucket: [{ id: "mt-b1-0a", difficulty: 0 }, {id: "mt-b1-0b", difficulty: 0 }, { id: "mt-b1-1a", difficulty: 1 }]
 * B2_bucket: [{ id: "mt-b2-0a", difficulty: 0 }, {id: "mt-b2-0b", difficulty: 0 }, { id: "mt-b2-1a", difficulty: 1 }]
 * B3_bucket: [{ id: "mt-b3-0a", difficulty: 0 }, {id: "mt-b3-0b", difficulty: 0 }, { id: "mt-b3-1a", difficulty: 1 }]
 * ... and so on. 
 * There should be buckets for B1, B2, B3, C1, C2, C3, C4, C5 - so 8 buckets in total
 * Then assign exercises for each week starting with the easiest exercise in each bucket and working up
 * Assign exercises in the following order:
 * Off Season: B1, B2, B3, C5, repeating until end date is reached
 * Prep: C1, C2, C3, C5, repeating until end date is reached
 * Pre-Comp: C4, C5, repeating until end date is reached
 * 
 * ONLY ASSIGN 1 EXERCISE PER WEEK FOR MENTAL TOUGHNESS
 * DO NOT ASSIGN EXERCISES DURING THE COMPETITION PERIOD
 * 
 * Schedule mental wellness:
 * First, group all exercises into skill buckets sorted by difficulty and then alphabetically.
 * Mental wellness will have the following skills: M, P1, P2, P3, E1, E2, R, A, L1, L2, L3, U, S
 * For example:
 * M_bucket: [{ id: "mw-m-0a", difficulty: 0 }, {id: "mw-m-0b", difficulty: 0 }, { id: "mw-m-1a", difficulty: 1 }]
 * P1_bucket: [{ id: "mw-p1-0a", difficulty: 0 }, {id: "mw-p1-0b", difficulty: 0 }, { id: "mw-p1-1a", difficulty: 1 }]
 * ... and so on. 
 * 
 * There should be buckets for M, P1, P2, P3, E1, E2, R, A, L1, L2, L3, U, S - so 13 buckets in total
 * Then assign exercises for each week starting with the easiest exercise in each bucket and working up
 * 
 * Assign exercises in the following order:
 * M, [P1, P2, P3], [E1, E2], R, M, A, [P1, P2, P3], [L1, L2, L3], U, S
 * In the above, the brackets indicate that one exercise from that bucket should be assigned per week.
 * Assign the exercises in the order they appear in the array. For example:
 * M, P1, E1, R, M, A, P2, L1, U, S, M, P3, E2, R, M, A, P1, L2, U, S...
 * 
 * ONLY ASSIGN 1 EXERCISE PER WEEK FOR MENTAL WELLNESS
 * DO NOT ASSIGN EXERCISES DURING THE COMPETITION PERIOD
 */

export const generatePeriodization = (
  offSeasonStartDate: Date,
  offSeasonEndDate: Date,
  prepStartDate: Date,
  prepEndDate: Date,
  preCompStartDate: Date,
  preCompEndDate: Date,
  compStartDate: Date,
  compEndDate: Date,
  allExercises: Exercise[],
  scheduleMentalToughness: boolean,
  scheduleMentalWellness: boolean
) => {
//   const entries: PeriodizationEntry[] = [];
//   const start = new Date(startDate);

//   const mtExercises = allExercises.filter((e) =>
//     e.id.toLowerCase().startsWith("mt")
//   );
//   const mwExercises = allExercises.filter((e) =>
//     e.id.toLowerCase().startsWith("mw")
//   );

//   for (let week = 1; week <= weeks; week++) {
//     const assignmentDate = new Date(start);
//     assignmentDate.setDate(start.getDate() + (week - 1) * 7);

//     // Set 1 mt and 1 mw exercise every week
//     if (week <= mtExercises.length) {
//       entries.push({
//         week,
//         assignmentDate: assignmentDate.toISOString().split("T")[0],
//         exerciseType: "MT exercise",
//         exerciseName: mtExercises[week - 1].name,
//         exerciseId: mtExercises[week - 1].id,
//       });
//     }
//     if (week <= mwExercises.length) {
//       entries.push({
//         week,
//         assignmentDate: assignmentDate.toISOString().split("T")[0],
//         exerciseType: "MW exercise",
//         exerciseName: mwExercises[week - 1].name,
//         exerciseId: mwExercises[week - 1].id,
//       });
//     }
//     if (week <= allVisualizations.length) {
//       entries.push({
//         week,
//         assignmentDate: assignmentDate.toISOString().split("T")[0],
//         exerciseType: "visualization",
//         exerciseName: allVisualizations[week - 1].title,
//         exerciseId: allVisualizations[week - 1].id,
//       });
//     }
//   }

//   return entries;
};
